# fly.toml app configuration file generated for randpick on 2025-05-17T18:19:55+08:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'randpick'
primary_region = 'lax'

[build]

[[mounts]]
source = 'pb_data'
destination = '/app/pb/pb_data'

[http_service]
internal_port = 3000
force_https = true
auto_stop_machines = 'stop'
auto_start_machines = true
min_machines_running = 0
processes = ['app']

[[services]]
protocol = 'tcp'
internal_port = 8090
processes = ['app']
auto_stop_machines = 'stop'
auto_start_machines = true
min_machines_running = 0

[[services.ports]]
port = 8090
handlers = ['tls', 'http']

[[services.http_options]]
cors = true
allowed_origins = ["https://randpick.fly.dev"]


[[vm]]
memory = '256mb'
cpu_kind = 'shared'
cpus = 1
