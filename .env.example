# PocketBase URL and Admin Credentials
NEXT_PUBLIC_SITE_NAME="新城小学抽签系统"
NEXT_PUBLIC_POCKETBASE_URL="http://192.168.1.138:8090"
NEXT_PUBLIC_BASE_URL="http://192.168.1.138:3000"
POCKETBASE_ADMIN_EMAIL="<EMAIL>"
POCKETBASE_ADMIN_PASSWORD="daSyc1uJ0Sl"

# Next Auth
AUTH_SECRET="uiyadf768asdf786asd9876f"

# Next Auth Discord Provider
AUTH_DISCORD_ID=""
AUTH_DISCORD_SECRET=""

NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_c3BlY2lhbC1oZWRnZWhvZy0xMy5jbGVyay5hY2NvdW50cy5kZXYk
CLERK_SECRET_KEY=sk_test_BaMTiRRiqAneF6pUNYt45FQvoVdJKXEuPjSYiVL7Hy

# Optional: URL configuration for Clerk
NEXT_PUBLIC_CLERK_SIGN_IN_URL="/sign-in"
NEXT_PUBLIC_CLERK_SIGN_UP_URL="/sign-up"

# 测试环境配置

NEXT_PUBLIC_SKIP_AUTH_IN_TEST=false
NEXT_SERVER_ACTIONS_ENCRYPTION_KEY=uiyadf768asdf786asd9876f
