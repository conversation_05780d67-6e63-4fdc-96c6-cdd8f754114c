{"extends": "../.eslintrc.json", "rules": {"@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/no-unsafe-member-access": "off", "@typescript-eslint/no-unsafe-call": "off", "@typescript-eslint/no-unsafe-return": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/unbound-method": "off", "@typescript-eslint/no-unsafe-argument": "off", "react-hooks/rules-of-hooks": "off", "react-hooks/exhaustive-deps": "off"}, "env": {"jest": true}}