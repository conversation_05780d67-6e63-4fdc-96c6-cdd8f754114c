# 依赖目录
node_modules
.pnp
.pnp.js

# 测试相关
coverage

# 数据库文件
prisma/db.sqlite
prisma/db.sqlite-journal
*.sqlite

# Next.js 生成文件
.next
out
next-env.d.ts

# 生产构建目录
build

# 系统文件
.DS_Store
*.pem

# 调试日志
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# 本地环境文件
.env
.env*.local

# Vercel 相关
.vercel

# TypeScript 构建信息
*.tsbuildinfo

# IDE 文件
.idea
test-results
playwright-report
pb/pb_data

# Clerk 配置
.clerk
fly.toml
