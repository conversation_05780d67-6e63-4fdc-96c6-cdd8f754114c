import { NextResponse } from "next/server";
import { getCachedCurrentUser } from "~/services/auth-cache-simple";
import { activityService } from "~/services/activity";

/**
 * 获取当前用户的活动列表
 * 这个 API 路由确保只返回当前登录用户创建的活动
 */
export async function GET() {
  try {
    // 获取当前用户信息
    const user = await getCachedCurrentUser();
    
    if (!user) {
      return NextResponse.json(
        { error: "未授权访问" },
        { status: 401 }
      );
    }

    // 获取当前用户的活动列表
    const activities = await activityService.getAdminActivityList(user.id);

    return NextResponse.json({
      success: true,
      activities,
      userId: user.id,
    });
  } catch (error) {
    console.error("获取用户活动列表失败:", error);
    
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : "获取活动列表失败",
        success: false 
      },
      { status: 500 }
    );
  }
}
