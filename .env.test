# PocketBase 服务器配置
NEXT_PUBLIC_SITE_NAME="新城小学王少博抽签系统(测试环境)"
NEXT_PUBLIC_POCKETBASE_URL=http://127.0.0.1:8090

# 测试环境配置
NEXT_PUBLIC_SKIP_AUTH_IN_TEST=false

# 测试用管理员账号
# 注意：该用户必须在 PocketBase 中设置 role=admin
TEST_ADMIN_USERNAME=admin
TEST_ADMIN_PASSWORD=xlu_omKO3lMLPVk

# PocketBase 管理员账号（用于脚本设置）
POCKETBASE_ADMIN_EMAIL=<EMAIL>
POCKETBASE_ADMIN_PASSWORD=daSyc1uJ0Sl

# Clerk 测试账号配置
# 注意：需要在 Clerk Dashboard 创建测试用户
TEST_USER_NAME=admin
TEST_USER_PASSWORD=xlu_omKO3lMLPVk

# Clerk API Keys

NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_c3BlY2lhbC1oZWRnZWhvZy0xMy5jbGVyay5hY2NvdW50cy5kZXYk
CLERK_SECRET_KEY=sk_test_BaMTiRRiqAneF6pUNYt45FQvoVdJKXEuPjSYiVL7Hy

# Clerk URL Configuration
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/
