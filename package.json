{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsc --noEmit", "dev": "next dev --turbopack -H 0.0.0.0", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start -H 0.0.0.0", "typecheck": "tsc --noEmit", "test:e2e": "cross-env NODE_ENV=test playwright test", "test": "npm run test:e2e"}, "dependencies": {"@clerk/localizations": "^3.15.2", "@clerk/nextjs": "^6.19.2", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@t3-oss/env-nextjs": "0.13.4", "@tailwindcss/postcss": "^4.1.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "lucide-react": "0.509.0", "next": "15.2.3", "pocketbase": "^0.26.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "sweetalert2": "^11.21.0", "sweetalert2-react-content": "^5.1.0", "tailwind-merge": "^3.2.0", "winston": "^3.17.0", "zod": "^3.24.4", "zustand": "^5.0.4"}, "devDependencies": {"@clerk/testing": "^1.7.0", "@eslint/eslintrc": "^3.3.1", "@faker-js/faker": "^9.8.0", "@playwright/test": "^1.52.0", "@tailwindcss/forms": "^0.5.10", "@types/node": "20.17.47", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "daisyui": "^5.0.35", "dotenv": "^16.5.0", "eslint": "^9.23.0", "eslint-config-next": "^15.2.3", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.7", "ts-node": "^10.9.2", "typescript": "^5.8.2", "typescript-eslint": "^8.27.0"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "packageManager": "npm@10.9.2"}