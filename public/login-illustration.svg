<svg viewBox="0 0 400 300"
    xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
        </linearGradient>
        <linearGradient id="card" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
        </linearGradient>
    </defs>

    <!-- Background -->
    <rect width="400" height="300" fill="url(#bg)"/>

    <!-- Floating shapes -->
    <circle cx="80" cy="60" r="25" fill="rgba(255,255,255,0.1)" opacity="0.6"/>
    <circle cx="320" cy="80" r="35" fill="rgba(255,255,255,0.1)" opacity="0.4"/>
    <circle cx="350" cy="220" r="20" fill="rgba(255,255,255,0.1)" opacity="0.7"/>

    <!-- Main illustration -->
    <g transform="translate(50, 80)">
        <!-- Computer/Laptop -->
        <rect x="80" y="60" width="140" height="90" rx="8" fill="url(#card)" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
        <rect x="90" y="70" width="120" height="70" rx="4" fill="#1e293b"/>

        <!-- Screen content (representing lottery/draw) -->
        <circle cx="130" cy="90" r="8" fill="#10b981"/>
        <circle cx="150" cy="90" r="8" fill="#f59e0b"/>
        <circle cx="170" cy="90" r="8" fill="#ef4444"/>

        <!-- Lottery balls/numbers -->
        <text x="150" y="125" text-anchor="middle" fill="#64748b" font-family="Arial" font-size="12" font-weight="bold">抽签</text>

        <!-- Keyboard -->
        <rect x="75" y="155" width="150" height="8" rx="4" fill="rgba(255,255,255,0.3)"/>
    </g>

    <!-- Decorative elements -->
    <path d="M20,200 Q60,180 100,200 T180,200" stroke="rgba(255,255,255,0.2)" stroke-width="2" fill="none"/>
    <path d="M220,250 Q260,230 300,250 T380,250" stroke="rgba(255,255,255,0.2)" stroke-width="2" fill="none"/>

    <!-- Stars/sparkles -->
    <g fill="rgba(255,255,255,0.6)">
        <path d="M100,40 L102,46 L108,46 L103,50 L105,56 L100,52 L95,56 L97,50 L92,46 L98,46 Z"/>
        <path d="M300,45 L301,48 L304,48 L302,50 L303,53 L300,51 L297,53 L298,50 L296,48 L299,48 Z"/>
        <path d="M60,180 L61,183 L64,183 L62,185 L63,188 L60,186 L57,188 L58,185 L56,183 L59,183 Z"/>
    </g>
</svg>
